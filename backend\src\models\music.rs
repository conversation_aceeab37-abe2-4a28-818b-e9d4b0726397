use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct MusicTrack {
    pub id: i64,
    pub title: String,
    pub artist: String,
    pub album: Option<String>,
    pub duration: i32, // in seconds
    pub file_url: String,
    pub cover_url: Option<String>,
    pub genre: Option<String>,
    pub file_size: i64, // in bytes
    pub status: String, // active, processing, error
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MusicTrackCreate {
    pub title: String,
    pub artist: String,
    pub album: Option<String>,
    pub genre: Option<String>,
    pub duration: Option<i32>,
    pub file_size: Option<i64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MusicTrackUpdate {
    pub title: Option<String>,
    pub artist: Option<String>,
    pub album: Option<String>,
    pub genre: Option<String>,
    pub duration: Option<i32>,
    pub status: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MusicTracksResponse {
    pub tracks: Vec<MusicTrack>,
    pub total_tracks: i64,
    pub total_pages: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MusicUploadResponse {
    pub success: bool,
    pub message: Option<String>,
    pub track: Option<MusicTrack>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MusicSearchQuery {
    pub search: Option<String>,
    pub genre: Option<String>,
    pub page: Option<i64>,
    pub limit: Option<i64>,
}

// Database row structure for SQLite
#[derive(Debug, FromRow)]
pub struct MusicTrackRow {
    pub id: i64,
    pub title: String,
    pub artist: String,
    pub album: Option<String>,
    pub duration: i32,
    pub file_url: String,
    pub cover_url: Option<String>,
    pub genre: Option<String>,
    pub file_size: i64,
    pub status: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
