use axum::{
    routing::{delete, get, post, put},
    Router,
};

use tower_http::trace::TraceLayer;

use crate::config::Settings;
use crate::database::Database;
use crate::handlers::health;
use crate::handlers::{admin, admin_api, auth, blog, chat};
use crate::middleware::{auth::auth_middleware, cors::cors_layer};

/// Create the main application router with all routes and middleware
pub async fn create_app(database: Database, _settings: &Settings) -> Router<Database> {
    Router::new()
        .merge(public_routes())
        .merge(admin_routes(database))
        // Apply middleware layers
        .layer(TraceLayer::new_for_http())
        .layer(cors_layer())
}

/// Public routes that don't require authentication
fn public_routes() -> Router<Database> {
    Router::new()
        // Blog API routes
        .route("/api/posts", get(blog::get_posts))
        .route("/api/posts/:slug", get(blog::get_post_by_slug))
        .route("/api/categories", get(blog::get_categories))
        .route(
            "/api/categories/:category",
            get(blog::get_posts_by_category),
        )
        // AI Chat route
        .route("/api/chat", post(chat::chat_with_ai))
        // Admin login (public)
        .route("/api/admin/login", post(admin_api::admin_login))
        // Health check endpoints
        .route("/api/health", get(health::health_check))
        .route("/api/health/detailed", get(health::health_detailed))
        .route("/api/health/ready", get(health::readiness_check))
        .route("/api/health/live", get(health::liveness_check))
}

/// Admin routes that require authentication
fn admin_routes(database: Database) -> Router<Database> {
    Router::new()
        // ============================================================================
        // New Admin API (Frontend Management)
        // ============================================================================
        // Dashboard
        .route(
            "/api/admin/dashboard/stats",
            get(admin_api::get_dashboard_stats),
        )
        // Blog Post Management (New API)
        .route("/api/admin/posts", get(admin_api::get_admin_posts))
        .route("/api/admin/posts", post(admin_api::create_admin_post))
        .route("/api/admin/posts/:id", get(admin_api::get_admin_post))
        .route("/api/admin/posts/:id", put(admin_api::update_admin_post))
        .route("/api/admin/posts/:id", delete(admin_api::delete_admin_post))
        .route(
            "/api/admin/posts/bulk-delete",
            post(admin_api::bulk_delete_posts),
        )
        .route(
            "/api/admin/posts/:id/publish",
            post(admin_api::publish_post),
        )
        .route(
            "/api/admin/posts/:id/unpublish",
            post(admin_api::unpublish_post),
        )
        // Music Management
        .route("/api/admin/music", get(admin_api::get_music_tracks))
        .route("/api/admin/music/upload", post(admin_api::upload_music))
        .route("/api/admin/music/:id", get(admin_api::get_music_track))
        .route(
            "/api/admin/music/:id",
            delete(admin_api::delete_music_track),
        )
        .route(
            "/api/admin/music/bulk-delete",
            post(admin_api::bulk_delete_music),
        )
        // File Upload
        .route("/api/admin/upload", post(admin_api::upload_file))
        // ============================================================================
        // Legacy Admin API (Keep for backward compatibility)
        // ============================================================================
        // Authentication
        .route("/api/admin/verify", get(auth::verify_token))
        // Dashboard
        .route("/api/admin/dashboard", get(admin::get_dashboard))
        // Post management (legacy)
        .route("/api/admin/posts/legacy", get(admin::get_all_posts))
        .route("/api/admin/posts/legacy", post(admin::create_post))
        .route("/api/admin/posts/legacy/:slug", get(admin::get_post))
        .route("/api/admin/posts/legacy/:slug", put(admin::update_post))
        .route("/api/admin/posts/legacy/:slug", delete(admin::delete_post))
        .route(
            "/api/admin/posts/:slug/markdown",
            get(admin::get_post_markdown),
        )
        // Category management
        .route("/api/admin/categories", get(admin::get_categories))
        // AI assistance
        .route("/api/admin/ai-assist", post(admin::ai_assist))
        // System status
        .route("/api/admin/system-status", get(admin::get_system_status))
        // Statistics trends
        .route("/api/admin/stats-trends", get(admin::get_stats_trends))
        // File upload (legacy)
        .route("/api/admin/upload/image", post(admin::upload_image))
        // Apply authentication middleware to protected routes only
        .layer(axum::middleware::from_fn_with_state(
            database,
            auth_middleware,
        ))
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::Settings;

    #[tokio::test]
    async fn test_routes_creation() {
        let settings = Settings::new().unwrap();
        let database = Database::new(":memory:").await.unwrap();
        let _app = create_app(database, &settings).await;
        // Test passes if app creation succeeds
    }
}
