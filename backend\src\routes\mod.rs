use axum::{
    middleware,
    routing::{get, post, put, delete},
    Router,
};
use crate::config::Config;
use crate::database::Database;
use crate::handlers::*;
use crate::middleware::auth::admin_middleware;

pub async fn create_app(database: Database, config: &Config) -> Router {
    // Public routes (no authentication required)
    let public_routes = Router::new()
        // Post public routes
        .route("/api/post/list", get(PostHandler::list_posts))
        .route("/api/post/get/:id", get(PostHandler::get_post))
        // Music public routes
        .route("/api/music/list", get(MusicHandler::list_music))
        .route("/api/music/get/:id", get(MusicHandler::get_music))
        // Download public routes
        .route("/api/download/download_file/:id", get(DownloadHandler::download_file))
        .route("/api/download/get_file_list", get(DownloadHandler::get_file_list))
        .route("/api/download/get_file/:id", get(DownloadHandler::get_file))
        // Category public routes
        .route("/api/category/list", get(CategoryHandler::list_categories))
        // Tag public routes
        .route("/api/tag/list", get(TagHandler::list_tags));

    // Admin routes (authentication required)
    let admin_routes = Router::new()
        // Post admin routes
        .route("/api/post/create", post(PostHandler::create_post))
        .route("/api/post/update/:id", put(PostHandler::update_post))
        .route("/api/post/delete/:id", delete(PostHandler::delete_post))
        .route("/api/post/upload_post_image", post(PostHandler::upload_post_image))
        .route("/api/post/update_cover/:id", put(PostHandler::update_post_cover))
        .route("/api/post/get_tags/:id", get(PostHandler::get_post_tags))
        .route("/api/post/update_tags/:id", put(PostHandler::update_post_tags))
        // Music admin routes
        .route("/api/music/create", post(MusicHandler::create_music))
        .route("/api/music/update/:id", put(MusicHandler::update_music))
        .route("/api/music/delete/:id", delete(MusicHandler::delete_music))
        .route("/api/music/upload_music", post(MusicHandler::upload_music))
        .route("/api/music/upload_music_cover/:id", post(MusicHandler::upload_music_cover))
        // Download admin routes
        .route("/api/download/upload_file", post(DownloadHandler::upload_file))
        .route("/api/download/delete_file/:id", delete(DownloadHandler::delete_file))
        // Category admin routes
        .route("/api/category/create", post(CategoryHandler::create_category))
        .route("/api/category/update/:id", put(CategoryHandler::update_category))
        .route("/api/category/delete/:id", delete(CategoryHandler::delete_category))
        // Tag admin routes
        .route("/api/tag/create", post(TagHandler::create_tag))
        .route("/api/tag/update/:id", put(TagHandler::update_tag))
        .route("/api/tag/delete/:id", delete(TagHandler::delete_tag))
        // Apply admin authentication middleware
        .layer(middleware::from_fn_with_state(config.clone(), admin_middleware));

    // Combine all routes
    Router::new()
        .merge(public_routes)
        .merge(admin_routes)
        .with_state(database)
        .with_state(config.clone())
}
