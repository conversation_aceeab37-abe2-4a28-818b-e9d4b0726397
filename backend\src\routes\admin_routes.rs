// Complete Admin API Routes

use axum::{
    routing::{delete, get, post, put},
    Router,
};

use crate::database::Database;
use crate::handlers::admin_api;

/// Create admin routes for the management interface
pub fn create_admin_routes() -> Router<Database> {
    Router::new()
        // ============================================================================
        // Authentication & Dashboard
        // ============================================================================
        .route("/api/admin/login", post(admin_api::admin_login))
        .route("/api/admin/dashboard/stats", get(admin_api::get_dashboard_stats))
        
        // ============================================================================
        // Blog Post Management
        // ============================================================================
        .route("/api/admin/posts", get(admin_api::get_admin_posts))
        .route("/api/admin/posts", post(admin_api::create_admin_post))
        .route("/api/admin/posts/:id", get(admin_api::get_admin_post))
        .route("/api/admin/posts/:id", put(admin_api::update_admin_post))
        .route("/api/admin/posts/:id", delete(admin_api::delete_admin_post))
        .route("/api/admin/posts/bulk-delete", post(admin_api::bulk_delete_posts))
        .route("/api/admin/posts/:id/publish", post(admin_api::publish_post))
        .route("/api/admin/posts/:id/unpublish", post(admin_api::unpublish_post))
        
        // ============================================================================
        // Music Management
        // ============================================================================
        .route("/api/admin/music", get(admin_api::get_music_tracks))
        .route("/api/admin/music/upload", post(admin_api::upload_music))
        .route("/api/admin/music/:id", get(admin_api::get_music_track))
        .route("/api/admin/music/:id", delete(admin_api::delete_music_track))
        .route("/api/admin/music/bulk-delete", post(admin_api::bulk_delete_music))
        
        // ============================================================================
        // File Upload
        // ============================================================================
        .route("/api/admin/upload", post(admin_api::upload_file))
}

/// Create public routes (no authentication required)
pub fn create_public_routes() -> Router<Database> {
    Router::new()
        // Login endpoint is public
        .route("/api/admin/login", post(admin_api::admin_login))
}
