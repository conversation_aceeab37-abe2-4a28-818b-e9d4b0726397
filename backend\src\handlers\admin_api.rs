// Complete Admin API handlers for blog and music management

use crate::database::Database;
use crate::models::{
    BlogPost, BlogPostCreate, BlogPostUpdate, MusicSearchQuery, MusicTrack, MusicTrackCreate,
    MusicUploadResponse, PostSearchQuery,
};
use crate::services::{BlogService, MusicService};
use crate::utils::{AppError, AppResult};
use axum::{
    extract::{Path, Query, State},
    response::Json,
};
use axum_extra::extract::Multipart;
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::path::Path as StdPath;
use tokio::fs;
use tokio::io::AsyncWriteExt;

// ============================================================================
// Request/Response Types
// ============================================================================

#[derive(Debug, Serialize, Deserialize)]
pub struct BulkDeleteRequest {
    pub ids: Vec<i64>,
}

// ============================================================================
// Authentication & Dashboard
// ============================================================================

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub success: bool,
    pub token: Option<String>,
    pub message: Option<String>,
    pub user: Option<AdminUser>,
}

#[derive(Debug, Serialize)]
pub struct AdminUser {
    pub id: String,
    pub username: String,
    pub email: Option<String>,
    pub role: String,
    #[serde(rename = "lastLogin")]
    pub last_login: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct DashboardStats {
    #[serde(rename = "totalPosts")]
    pub total_posts: i64,
    #[serde(rename = "totalCategories")]
    pub total_categories: i64,
    #[serde(rename = "totalViews")]
    pub total_views: i64,
    #[serde(rename = "recentPosts")]
    pub recent_posts: i64,
}

/// Admin login endpoint
pub async fn admin_login(
    Json(login_request): Json<LoginRequest>,
) -> AppResult<Json<LoginResponse>> {
    // Simple hardcoded authentication for demo
    if login_request.username == "admin" && login_request.password == "admin123456" {
        let user = AdminUser {
            id: "1".to_string(),
            username: "admin".to_string(),
            email: Some("<EMAIL>".to_string()),
            role: "admin".to_string(),
            last_login: Some(chrono::Utc::now().to_rfc3339()),
        };

        Ok(Json(LoginResponse {
            success: true,
            token: Some("admin123456".to_string()),
            message: Some("Login successful".to_string()),
            user: Some(user),
        }))
    } else {
        Ok(Json(LoginResponse {
            success: false,
            token: None,
            message: Some("Invalid username or password".to_string()),
            user: None,
        }))
    }
}

/// Get dashboard statistics
pub async fn get_dashboard_stats(
    State(database): State<Database>,
) -> AppResult<Json<DashboardStats>> {
    let blog_service = BlogService::new(database.clone());
    let music_service = MusicService::new(database);

    // Get blog stats
    let posts = blog_service.get_all_posts().await?;
    let total_posts = posts.len() as i64;

    // Get unique categories
    let categories: std::collections::HashSet<String> =
        posts.iter().map(|p| p.category.clone()).collect();
    let total_categories = categories.len() as i64;

    // Mock total views (in real app, this would come from analytics)
    let total_views = 1234i64;

    // Recent posts (last 7 days)
    let recent_posts = posts
        .iter()
        .filter(|p| {
            if let Ok(date) = chrono::DateTime::parse_from_rfc3339(&p.date) {
                let now = chrono::Utc::now();
                let diff = now.signed_duration_since(date);
                diff.num_days() <= 7
            } else {
                false
            }
        })
        .count() as i64;

    Ok(Json(DashboardStats {
        total_posts,
        total_categories,
        total_views,
        recent_posts,
    }))
}

// ============================================================================
// Blog Post Management
// ============================================================================

/// Get all posts with filtering and pagination
pub async fn get_admin_posts(
    State(database): State<Database>,
    Query(query): Query<PostSearchQuery>,
) -> AppResult<Json<Vec<BlogPost>>> {
    let blog_service = BlogService::new(database);
    let mut posts = blog_service.get_all_posts().await?;

    // Apply filters
    if let Some(ref search) = query.search {
        let search_lower = search.to_lowercase();
        posts.retain(|post| {
            post.title.to_lowercase().contains(&search_lower)
                || post.excerpt.to_lowercase().contains(&search_lower)
                || post
                    .tags
                    .iter()
                    .any(|tag| tag.to_lowercase().contains(&search_lower))
        });
    }

    if let Some(ref category) = query.category {
        posts.retain(|post| post.category == *category);
    }

    if let Some(ref status) = query.status {
        if status != "all" {
            posts.retain(|post| post.status == *status);
        }
    }

    // Sort by creation date (newest first)
    posts.sort_by(|a, b| b.date.cmp(&a.date));

    Ok(Json(posts))
}

/// Get a single post by ID
pub async fn get_admin_post(
    State(database): State<Database>,
    Path(id): Path<i64>,
) -> AppResult<Json<BlogPost>> {
    let blog_service = BlogService::new(database);

    match blog_service.get_post_by_id(id).await? {
        Some(post) => Ok(Json(post)),
        None => Err(AppError::not_found("Post not found".to_string())),
    }
}

/// Create a new post
pub async fn create_admin_post(
    State(database): State<Database>,
    Json(post_data): Json<BlogPostCreate>,
) -> AppResult<Json<BlogPost>> {
    let blog_service = BlogService::new(database);
    let post = blog_service.create_post(post_data).await?;
    Ok(Json(post))
}

/// Update an existing post
pub async fn update_admin_post(
    State(database): State<Database>,
    Path(id): Path<i64>,
    Json(post_data): Json<BlogPostUpdate>,
) -> AppResult<Json<BlogPost>> {
    let blog_service = BlogService::new(database);

    match blog_service.update_post(id, post_data).await? {
        Some(post) => Ok(Json(post)),
        None => Err(AppError::not_found("Post not found".to_string())),
    }
}

/// Delete a post
pub async fn delete_admin_post(
    State(database): State<Database>,
    Path(id): Path<i64>,
) -> AppResult<Json<serde_json::Value>> {
    let blog_service = BlogService::new(database);

    if blog_service.delete_post(id).await? {
        Ok(Json(
            json!({ "success": true, "message": "Post deleted successfully" }),
        ))
    } else {
        Err(AppError::not_found("Post not found".to_string()))
    }
}

/// Bulk delete posts
pub async fn bulk_delete_posts(
    State(database): State<Database>,
    Json(request): Json<BulkDeleteRequest>,
) -> AppResult<Json<serde_json::Value>> {
    let blog_service = BlogService::new(database);
    let mut deleted_count = 0;

    for id in request.ids {
        if blog_service.delete_post(id).await? {
            deleted_count += 1;
        }
    }

    Ok(Json(json!({
        "success": true,
        "message": format!("{} posts deleted successfully", deleted_count),
        "deleted_count": deleted_count
    })))
}

/// Publish a post
pub async fn publish_post(
    State(database): State<Database>,
    Path(id): Path<i64>,
) -> AppResult<Json<BlogPost>> {
    let blog_service = BlogService::new(database);

    let update_data = BlogPostUpdate {
        status: Some("published".to_string()),
        date: Some(chrono::Utc::now().to_rfc3339()),
        ..Default::default()
    };

    match blog_service.update_post(id, update_data).await? {
        Some(post) => Ok(Json(post)),
        None => Err(AppError::not_found("Post not found".to_string())),
    }
}

/// Unpublish a post (set to draft)
pub async fn unpublish_post(
    State(database): State<Database>,
    Path(id): Path<i64>,
) -> AppResult<Json<BlogPost>> {
    let blog_service = BlogService::new(database);

    let update_data = BlogPostUpdate {
        status: Some("draft".to_string()),
        ..Default::default()
    };

    match blog_service.update_post(id, update_data).await? {
        Some(post) => Ok(Json(post)),
        None => Err(AppError::not_found("Post not found".to_string())),
    }
}

// ============================================================================
// Music Management
// ============================================================================

/// Get all music tracks with filtering and pagination
pub async fn get_music_tracks(
    State(database): State<Database>,
    Query(query): Query<MusicSearchQuery>,
) -> AppResult<Json<Vec<MusicTrack>>> {
    let music_service = MusicService::new(database);
    let response = music_service.get_tracks(query).await?;
    Ok(Json(response.tracks))
}

/// Get a single music track by ID
pub async fn get_music_track(
    State(database): State<Database>,
    Path(id): Path<i64>,
) -> AppResult<Json<MusicTrack>> {
    let music_service = MusicService::new(database);

    match music_service.get_track_by_id(id).await? {
        Some(track) => Ok(Json(track)),
        None => Err(AppError::not_found("Music track not found".to_string())),
    }
}

/// Upload music file with metadata
pub async fn upload_music(
    State(database): State<Database>,
    mut multipart: Multipart,
) -> AppResult<Json<MusicUploadResponse>> {
    let music_service = MusicService::new(database);

    let mut title = String::new();
    let mut artist = String::new();
    let mut album = None;
    let mut genre = None;
    let mut music_file_data = None;
    let mut music_filename = None;
    let mut cover_file_data = None;
    let mut cover_filename = None;

    // Process multipart form data
    while let Some(field) = multipart
        .next_field()
        .await
        .map_err(|e| AppError::bad_request(format!("Failed to read multipart field: {}", e)))?
    {
        let name = field.name().unwrap_or("").to_string();

        match name.as_str() {
            "title" => {
                title = field
                    .text()
                    .await
                    .map_err(|e| AppError::bad_request(format!("Failed to read title: {}", e)))?;
            }
            "artist" => {
                artist = field
                    .text()
                    .await
                    .map_err(|e| AppError::bad_request(format!("Failed to read artist: {}", e)))?;
            }
            "album" => {
                let album_text = field
                    .text()
                    .await
                    .map_err(|e| AppError::bad_request(format!("Failed to read album: {}", e)))?;
                if !album_text.is_empty() {
                    album = Some(album_text);
                }
            }
            "genre" => {
                let genre_text = field
                    .text()
                    .await
                    .map_err(|e| AppError::bad_request(format!("Failed to read genre: {}", e)))?;
                if !genre_text.is_empty() {
                    genre = Some(genre_text);
                }
            }
            "music" => {
                music_filename = field.file_name().map(|s| s.to_string());
                music_file_data = Some(field.bytes().await.map_err(|e| {
                    AppError::bad_request(format!("Failed to read music file: {}", e))
                })?);
            }
            "cover" => {
                cover_filename = field.file_name().map(|s| s.to_string());
                cover_file_data = Some(field.bytes().await.map_err(|e| {
                    AppError::bad_request(format!("Failed to read cover file: {}", e))
                })?);
            }
            _ => {
                // Skip unknown fields
                let _ = field.bytes().await;
            }
        }
    }

    // Validate required fields
    if title.is_empty() {
        return Err(AppError::bad_request("Title is required".to_string()));
    }
    if artist.is_empty() {
        return Err(AppError::bad_request("Artist is required".to_string()));
    }
    if music_file_data.is_none() {
        return Err(AppError::bad_request("Music file is required".to_string()));
    }

    let music_data = music_file_data.unwrap();
    let filename = music_filename.unwrap_or_else(|| format!("{}-{}.mp3", artist, title));

    // Create uploads directory if it doesn't exist
    let uploads_dir = StdPath::new("uploads/music");
    if !uploads_dir.exists() {
        fs::create_dir_all(uploads_dir).await.map_err(|e| {
            AppError::internal(format!("Failed to create uploads directory: {}", e))
        })?;
    }

    // Save music file
    let file_path = uploads_dir.join(&filename);
    let mut file = fs::File::create(&file_path)
        .await
        .map_err(|e| AppError::internal(format!("Failed to create music file: {}", e)))?;
    file.write_all(&music_data)
        .await
        .map_err(|e| AppError::internal(format!("Failed to write music file: {}", e)))?;

    let file_url = format!("/uploads/music/{}", filename);

    // Save cover file if provided
    let cover_url =
        if let (Some(cover_data), Some(cover_name)) = (&cover_file_data, &cover_filename) {
            let covers_dir = StdPath::new("uploads/covers");
            if !covers_dir.exists() {
                fs::create_dir_all(covers_dir).await.map_err(|e| {
                    AppError::internal(format!("Failed to create covers directory: {}", e))
                })?;
            }

            let cover_path = covers_dir.join(&cover_name);
            let mut cover_file = fs::File::create(&cover_path)
                .await
                .map_err(|e| AppError::internal(format!("Failed to create cover file: {}", e)))?;
            cover_file
                .write_all(&cover_data)
                .await
                .map_err(|e| AppError::internal(format!("Failed to write cover file: {}", e)))?;

            Some(format!("/uploads/covers/{}", cover_name))
        } else {
            None
        };

    // Create track record
    let track_data = MusicTrackCreate {
        title,
        artist,
        album,
        genre,
        duration: None, // Could be extracted from file metadata
        file_size: Some(music_data.len() as i64),
    };

    let track = music_service
        .create_track(track_data, file_url, cover_url)
        .await?;

    Ok(Json(MusicUploadResponse {
        success: true,
        message: Some("Music uploaded successfully".to_string()),
        track: Some(track),
    }))
}

/// Delete a music track
pub async fn delete_music_track(
    State(database): State<Database>,
    Path(id): Path<i64>,
) -> AppResult<Json<serde_json::Value>> {
    let music_service = MusicService::new(database);

    if music_service.delete_track(id).await? {
        Ok(Json(
            json!({ "success": true, "message": "Music track deleted successfully" }),
        ))
    } else {
        Err(AppError::not_found("Music track not found".to_string()))
    }
}

/// Bulk delete music tracks
pub async fn bulk_delete_music(
    State(database): State<Database>,
    Json(request): Json<BulkDeleteRequest>,
) -> AppResult<Json<serde_json::Value>> {
    let music_service = MusicService::new(database);
    let deleted_count = music_service.bulk_delete_tracks(request.ids).await?;

    Ok(Json(json!({
        "success": true,
        "message": format!("{} music tracks deleted successfully", deleted_count),
        "deleted_count": deleted_count
    })))
}

// ============================================================================
// File Upload (Generic)
// ============================================================================

#[derive(Debug, Serialize)]
pub struct UploadResponse {
    pub url: String,
    pub filename: String,
    pub size: i64,
    #[serde(rename = "mimeType")]
    pub mime_type: String,
}

/// Generic file upload endpoint
pub async fn upload_file(mut multipart: Multipart) -> AppResult<Json<UploadResponse>> {
    let mut file_data = None;
    let mut filename = None;
    let mut file_type = "document".to_string();

    while let Some(field) = multipart
        .next_field()
        .await
        .map_err(|e| AppError::bad_request(format!("Failed to read multipart field: {}", e)))?
    {
        let name = field.name().unwrap_or("").to_string();

        match name.as_str() {
            "file" => {
                filename = field.file_name().map(|s| s.to_string());
                file_data =
                    Some(field.bytes().await.map_err(|e| {
                        AppError::bad_request(format!("Failed to read file: {}", e))
                    })?);
            }
            "type" => {
                file_type = field
                    .text()
                    .await
                    .map_err(|e| AppError::bad_request(format!("Failed to read type: {}", e)))?;
            }
            _ => {
                let _ = field.bytes().await;
            }
        }
    }

    let data = file_data.ok_or_else(|| AppError::bad_request("No file provided".to_string()))?;
    let name = filename.unwrap_or_else(|| "upload".to_string());

    // Determine upload directory based on type
    let upload_dir = match file_type.as_str() {
        "image" => "uploads/images",
        "audio" => "uploads/music",
        _ => "uploads/files",
    };

    let uploads_path = StdPath::new(upload_dir);
    if !uploads_path.exists() {
        fs::create_dir_all(uploads_path)
            .await
            .map_err(|e| AppError::internal(format!("Failed to create upload directory: {}", e)))?;
    }

    let file_path = uploads_path.join(&name);
    let mut file = fs::File::create(&file_path)
        .await
        .map_err(|e| AppError::internal(format!("Failed to create file: {}", e)))?;
    file.write_all(&data)
        .await
        .map_err(|e| AppError::internal(format!("Failed to write file: {}", e)))?;

    let url = format!("/{}/{}", upload_dir, name);
    let mime_type = mime_guess::from_path(&name)
        .first_or_octet_stream()
        .to_string();

    Ok(Json(UploadResponse {
        url,
        filename: name,
        size: data.len() as i64,
        mime_type,
    }))
}
