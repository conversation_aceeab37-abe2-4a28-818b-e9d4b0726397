use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct BlogPost {
    pub id: i64,
    pub title: String,
    pub excerpt: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub content: Option<String>,
    pub slug: String,
    #[serde(rename = "publishDate")]
    pub date: String, // ISO 8601 format for API compatibility
    pub author: String,
    #[serde(rename = "readTime")]
    pub read_time: i32,
    pub category: String,
    pub tags: Vec<String>,
    pub featured: bool,
    pub status: String, // draft, published
    #[serde(skip_serializing_if = "Option::is_none")]
    pub created_at: Option<DateTime<Utc>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct BlogPostCreate {
    pub title: String,
    pub excerpt: String,
    pub content: String,
    pub slug: Option<String>, // Auto-generated if not provided
    #[serde(rename = "publishDate")]
    pub date: Option<String>, // Current date if not provided
    pub author: Option<String>,
    #[serde(rename = "readTime")]
    pub read_time: Option<i32>,
    pub category: String,
    pub tags: Vec<String>,
    pub featured: Option<bool>,
    pub status: String, // draft, published
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct BlogPostUpdate {
    pub title: Option<String>,
    pub excerpt: Option<String>,
    pub content: Option<String>,
    pub slug: Option<String>,
    #[serde(rename = "publishDate")]
    pub date: Option<String>,
    pub author: Option<String>,
    #[serde(rename = "readTime")]
    pub read_time: Option<i32>,
    pub category: Option<String>,
    pub tags: Option<Vec<String>>,
    pub featured: Option<bool>,
    pub status: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlogPostsResponse {
    pub posts: Vec<BlogPost>,
    #[serde(rename = "totalPosts")]
    pub total_posts: i64,
    #[serde(rename = "totalPages")]
    pub total_pages: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlogPostResponse {
    pub success: bool,
    pub message: Option<String>,
    pub post: Option<BlogPost>,
}

// Database row structure for SQLite
#[derive(Debug, FromRow)]
pub struct BlogPostRow {
    pub id: i64,
    pub title: String,
    pub excerpt: String,
    pub content: String,
    pub slug: String,
    pub date: String,
    pub author: String,
    pub read_time: i32,
    pub category: String,
    pub tags: String, // JSON string
    pub featured: bool,
    pub status: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// Additional request/response types for admin API

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PostSearchQuery {
    pub search: Option<String>,
    pub category: Option<String>,
    pub status: Option<String>,
    pub page: Option<i64>,
    pub limit: Option<i64>,
}

impl From<BlogPostRow> for BlogPost {
    fn from(row: BlogPostRow) -> Self {
        let tags: Vec<String> = serde_json::from_str(&row.tags).unwrap_or_else(|_| vec![]);

        BlogPost {
            id: row.id,
            title: row.title,
            excerpt: row.excerpt,
            content: Some(row.content),
            slug: row.slug,
            date: row.date,
            author: row.author,
            read_time: row.read_time,
            category: row.category,
            tags,
            featured: row.featured,
            status: row.status,
            created_at: Some(row.created_at),
            updated_at: Some(row.updated_at),
        }
    }
}

impl BlogPost {
    pub fn without_content(mut self) -> Self {
        self.content = None;
        self
    }
}
