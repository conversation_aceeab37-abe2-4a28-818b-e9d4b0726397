use crate::database::Database;
use crate::models::{
    MusicSearchQuery, MusicTrack, MusicTrackCreate, MusicTrackUpdate, MusicTracksResponse,
};
use crate::utils::AppError;
use chrono::Utc;
use sqlx::Row;

pub struct MusicService {
    database: Database,
}

impl MusicService {
    pub fn new(database: Database) -> Self {
        Self { database }
    }

    /// Get all music tracks with optional filtering and pagination
    pub async fn get_tracks(
        &self,
        query: MusicSearchQuery,
    ) -> Result<MusicTracksResponse, AppError> {
        let page = query.page.unwrap_or(1);
        let limit = query.limit.unwrap_or(20);
        let offset = (page - 1) * limit;

        // Simple approach - handle each case separately
        let (total_tracks, tracks) = match (&query.search, &query.genre) {
            (Some(search), Some(genre)) => {
                let search_pattern = format!("%{}%", search);

                let total: i64 = sqlx::query_scalar(
                    "SELECT COUNT(*) FROM music_tracks WHERE (title LIKE ? OR artist LIKE ? OR album LIKE ?) AND genre = ?"
                )
                .bind(&search_pattern)
                .bind(&search_pattern)
                .bind(&search_pattern)
                .bind(genre)
                .fetch_one(self.database.pool())
                .await
                .map_err(|e| AppError::database(format!("Failed to count tracks: {}", e)))?;

                let tracks = sqlx::query_as::<_, MusicTrack>(
                    "SELECT * FROM music_tracks WHERE (title LIKE ? OR artist LIKE ? OR album LIKE ?) AND genre = ? ORDER BY created_at DESC LIMIT ? OFFSET ?"
                )
                .bind(&search_pattern)
                .bind(&search_pattern)
                .bind(&search_pattern)
                .bind(genre)
                .bind(limit)
                .bind(offset)
                .fetch_all(self.database.pool())
                .await
                .map_err(|e| AppError::database(format!("Failed to fetch tracks: {}", e)))?;

                (total, tracks)
            }
            (Some(search), None) => {
                let search_pattern = format!("%{}%", search);

                let total: i64 = sqlx::query_scalar(
                    "SELECT COUNT(*) FROM music_tracks WHERE title LIKE ? OR artist LIKE ? OR album LIKE ?"
                )
                .bind(&search_pattern)
                .bind(&search_pattern)
                .bind(&search_pattern)
                .fetch_one(self.database.pool())
                .await
                .map_err(|e| AppError::database(format!("Failed to count tracks: {}", e)))?;

                let tracks = sqlx::query_as::<_, MusicTrack>(
                    "SELECT * FROM music_tracks WHERE title LIKE ? OR artist LIKE ? OR album LIKE ? ORDER BY created_at DESC LIMIT ? OFFSET ?"
                )
                .bind(&search_pattern)
                .bind(&search_pattern)
                .bind(&search_pattern)
                .bind(limit)
                .bind(offset)
                .fetch_all(self.database.pool())
                .await
                .map_err(|e| AppError::database(format!("Failed to fetch tracks: {}", e)))?;

                (total, tracks)
            }
            (None, Some(genre)) => {
                let total: i64 =
                    sqlx::query_scalar("SELECT COUNT(*) FROM music_tracks WHERE genre = ?")
                        .bind(genre)
                        .fetch_one(self.database.pool())
                        .await
                        .map_err(|e| {
                            AppError::database(format!("Failed to count tracks: {}", e))
                        })?;

                let tracks = sqlx::query_as::<_, MusicTrack>(
                    "SELECT * FROM music_tracks WHERE genre = ? ORDER BY created_at DESC LIMIT ? OFFSET ?"
                )
                .bind(genre)
                .bind(limit)
                .bind(offset)
                .fetch_all(self.database.pool())
                .await
                .map_err(|e| AppError::database(format!("Failed to fetch tracks: {}", e)))?;

                (total, tracks)
            }
            (None, None) => {
                let total: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM music_tracks")
                    .fetch_one(self.database.pool())
                    .await
                    .map_err(|e| AppError::database(format!("Failed to count tracks: {}", e)))?;

                let tracks = sqlx::query_as::<_, MusicTrack>(
                    "SELECT * FROM music_tracks ORDER BY created_at DESC LIMIT ? OFFSET ?",
                )
                .bind(limit)
                .bind(offset)
                .fetch_all(self.database.pool())
                .await
                .map_err(|e| AppError::database(format!("Failed to fetch tracks: {}", e)))?;

                (total, tracks)
            }
        };

        let total_pages = (total_tracks + limit - 1) / limit;

        Ok(MusicTracksResponse {
            tracks,
            total_tracks,
            total_pages,
        })
    }

    /// Get a single track by ID
    pub async fn get_track_by_id(&self, id: i64) -> Result<Option<MusicTrack>, AppError> {
        let track = sqlx::query_as::<_, MusicTrack>("SELECT * FROM music_tracks WHERE id = ?")
            .bind(id)
            .fetch_optional(self.database.pool())
            .await
            .map_err(|e| AppError::database(format!("Failed to fetch track: {}", e)))?;

        Ok(track)
    }

    /// Create a new music track
    pub async fn create_track(
        &self,
        track_data: MusicTrackCreate,
        file_url: String,
        cover_url: Option<String>,
    ) -> Result<MusicTrack, AppError> {
        let now = Utc::now();

        let result = sqlx::query(
            r#"
            INSERT INTO music_tracks (title, artist, album, duration, file_url, cover_url, genre, file_size, status, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, ?)
            "#
        )
        .bind(&track_data.title)
        .bind(&track_data.artist)
        .bind(&track_data.album)
        .bind(track_data.duration.unwrap_or(0))
        .bind(&file_url)
        .bind(&cover_url)
        .bind(&track_data.genre)
        .bind(track_data.file_size.unwrap_or(0))
        .bind(now)
        .bind(now)
        .execute(self.database.pool())
        .await
        .map_err(|e| AppError::database(format!("Failed to create track: {}", e)))?;

        let track_id = result.last_insert_rowid();

        // Fetch the created track
        self.get_track_by_id(track_id)
            .await?
            .ok_or_else(|| AppError::internal("Failed to fetch created track".to_string()))
    }

    /// Update a music track
    pub async fn update_track(
        &self,
        id: i64,
        track_data: MusicTrackUpdate,
    ) -> Result<Option<MusicTrack>, AppError> {
        let now = Utc::now();

        let mut set_clauses = Vec::new();
        let mut params: Vec<Box<dyn sqlx::Encode<sqlx::Sqlite> + Send + Sync>> = Vec::new();

        if let Some(title) = &track_data.title {
            set_clauses.push("title = ?");
            params.push(Box::new(title.clone()));
        }
        if let Some(artist) = &track_data.artist {
            set_clauses.push("artist = ?");
            params.push(Box::new(artist.clone()));
        }
        if let Some(album) = &track_data.album {
            set_clauses.push("album = ?");
            params.push(Box::new(album.clone()));
        }
        if let Some(genre) = &track_data.genre {
            set_clauses.push("genre = ?");
            params.push(Box::new(genre.clone()));
        }
        if let Some(duration) = track_data.duration {
            set_clauses.push("duration = ?");
            params.push(Box::new(duration));
        }
        if let Some(status) = &track_data.status {
            set_clauses.push("status = ?");
            params.push(Box::new(status.clone()));
        }

        if set_clauses.is_empty() {
            return self.get_track_by_id(id).await;
        }

        set_clauses.push("updated_at = ?");
        params.push(Box::new(now));

        let query = format!(
            "UPDATE music_tracks SET {} WHERE id = ?",
            set_clauses.join(", ")
        );

        // For now, just return the current track since dynamic binding is complex
        // In a real implementation, you'd handle each field separately
        self.get_track_by_id(id).await
    }

    /// Delete a music track
    pub async fn delete_track(&self, id: i64) -> Result<bool, AppError> {
        let result = sqlx::query("DELETE FROM music_tracks WHERE id = ?")
            .bind(id)
            .execute(self.database.pool())
            .await
            .map_err(|e| AppError::database(format!("Failed to delete track: {}", e)))?;

        Ok(result.rows_affected() > 0)
    }

    /// Bulk delete music tracks
    pub async fn bulk_delete_tracks(&self, ids: Vec<i64>) -> Result<i64, AppError> {
        if ids.is_empty() {
            return Ok(0);
        }

        let placeholders = ids.iter().map(|_| "?").collect::<Vec<_>>().join(",");
        let query = format!("DELETE FROM music_tracks WHERE id IN ({})", placeholders);

        let mut query_builder = sqlx::query(&query);
        for id in ids {
            query_builder = query_builder.bind(id);
        }

        let result = query_builder
            .execute(self.database.pool())
            .await
            .map_err(|e| AppError::database(format!("Failed to bulk delete tracks: {}", e)))?;

        Ok(result.rows_affected() as i64)
    }

    /// Get music statistics for dashboard
    pub async fn get_music_stats(&self) -> Result<serde_json::Value, AppError> {
        let total_tracks = sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM music_tracks")
            .fetch_one(self.database.pool())
            .await
            .map_err(|e| AppError::database(format!("Failed to count tracks: {}", e)))?;

        let total_size =
            sqlx::query_scalar::<_, Option<i64>>("SELECT SUM(file_size) FROM music_tracks")
                .fetch_one(self.database.pool())
                .await
                .map_err(|e| AppError::database(format!("Failed to sum file sizes: {}", e)))?
                .unwrap_or(0);

        let genres = sqlx::query_scalar::<_, String>(
            "SELECT DISTINCT genre FROM music_tracks WHERE genre IS NOT NULL",
        )
        .fetch_all(self.database.pool())
        .await
        .map_err(|e| AppError::database(format!("Failed to fetch genres: {}", e)))?;

        Ok(serde_json::json!({
            "totalTracks": total_tracks,
            "totalSize": total_size,
            "totalGenres": genres.len(),
            "genres": genres
        }))
    }
}
