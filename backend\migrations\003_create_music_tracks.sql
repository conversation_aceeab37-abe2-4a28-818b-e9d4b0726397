-- Create music_tracks table
CREATE TABLE IF NOT EXISTS music_tracks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    artist TEXT NOT NULL,
    album TEXT,
    duration INTEGER DEFAULT 0, -- in seconds
    file_url TEXT NOT NULL,
    cover_url TEXT,
    genre TEXT,
    file_size INTEGER DEFAULT 0, -- in bytes
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'processing', 'error')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_music_tracks_title ON music_tracks(title);
CREATE INDEX IF NOT EXISTS idx_music_tracks_artist ON music_tracks(artist);
CREATE INDEX IF NOT EXISTS idx_music_tracks_genre ON music_tracks(genre);
CREATE INDEX IF NOT EXISTS idx_music_tracks_status ON music_tracks(status);
CREATE INDEX IF NOT EXISTS idx_music_tracks_created_at ON music_tracks(created_at);

-- <PERSON><PERSON> trigger to update updated_at timestamp
CREATE TRIGGER IF NOT EXISTS update_music_tracks_updated_at
    AFTER UPDATE ON music_tracks
    FOR EACH ROW
BEGIN
    UPDATE music_tracks SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
