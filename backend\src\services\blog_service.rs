use crate::database::{BlogRepository, Database};
use crate::models::{Blog<PERSON>ost, BlogPostCreate, BlogPostUpdate, BlogPostsResponse};
use crate::utils::{AppError, AppResult};
use chrono::Utc;

pub struct BlogService {
    database: Database,
}

impl BlogService {
    pub fn new(database: Database) -> Self {
        Self { database }
    }

    pub async fn get_posts(
        &self,
        page: Option<i64>,
        limit: Option<i64>,
        query: Option<String>,
    ) -> AppResult<BlogPostsResponse> {
        let page = page.unwrap_or(1);
        let limit = limit.unwrap_or(6);

        let repo = self.database.blog_repository();

        if let Some(search_query) = query {
            // Search posts
            let posts = repo.search_posts(&search_query).await?;
            let total_posts = posts.len() as i64;
            let total_pages = ((total_posts as f64) / (limit as f64)).ceil() as i64;

            Ok(BlogPostsResponse {
                posts,
                total_posts,
                total_pages,
            })
        } else {
            // Get paginated posts
            let (posts, total_posts) = repo.get_posts_without_content(page, limit).await?;
            let total_pages = ((total_posts as f64) / (limit as f64)).ceil() as i64;

            Ok(BlogPostsResponse {
                posts,
                total_posts,
                total_pages,
            })
        }
    }

    pub async fn get_post_by_slug(&self, slug: &str) -> AppResult<Option<BlogPost>> {
        let repo = self.database.blog_repository();
        Ok(repo.get_post_by_slug(slug).await?)
    }

    /// Get all posts (for admin)
    pub async fn get_all_posts(&self) -> AppResult<Vec<BlogPost>> {
        let repo = self.database.blog_repository();
        Ok(repo.get_all_posts_admin().await?)
    }

    /// Get post by ID (for admin)
    pub async fn get_post_by_id(&self, id: i64) -> AppResult<Option<BlogPost>> {
        let repo = self.database.blog_repository();
        Ok(repo.get_post_by_id_admin(id).await?)
    }

    /// Create a new post (for admin)
    pub async fn create_post(&self, post_data: BlogPostCreate) -> AppResult<BlogPost> {
        let repo = self.database.blog_repository();
        Ok(repo.create_post_admin(post_data).await?)
    }

    /// Update a post (for admin)
    pub async fn update_post(
        &self,
        id: i64,
        post_data: BlogPostUpdate,
    ) -> AppResult<Option<BlogPost>> {
        let repo = self.database.blog_repository();
        Ok(repo.update_post_admin(id, post_data).await?)
    }

    /// Delete a post (for admin)
    pub async fn delete_post(&self, id: i64) -> AppResult<bool> {
        let repo = self.database.blog_repository();
        Ok(repo.delete_post_admin(id).await?)
    }

    pub async fn get_categories(&self) -> AppResult<Vec<String>> {
        let repo = self.database.blog_repository();
        Ok(repo.get_all_categories().await?)
    }

    pub async fn get_posts_by_category(&self, category: &str) -> AppResult<Vec<BlogPost>> {
        let repo = self.database.blog_repository();
        Ok(repo.get_posts_by_category(category).await?)
    }

    // Legacy methods for backward compatibility
    pub async fn create_post_legacy(&self, post_data: BlogPostCreate) -> AppResult<BlogPost> {
        let repo = self.database.blog_repository();
        Ok(repo.create_post(post_data).await?)
    }

    pub async fn update_post_legacy(
        &self,
        slug: &str,
        post_data: BlogPostUpdate,
    ) -> AppResult<Option<BlogPost>> {
        let repo = self.database.blog_repository();

        // First check if post exists
        if let Some(existing_post) = repo.get_post_by_slug(slug).await? {
            // For now, return the existing post as update is complex with dynamic queries
            Ok(Some(existing_post))
        } else {
            Ok(None)
        }
    }

    pub async fn delete_post_legacy(&self, slug: &str) -> AppResult<bool> {
        let repo = self.database.blog_repository();
        Ok(repo.delete_post(slug).await?)
    }

    pub async fn get_all_posts_admin(
        &self,
        page: Option<i64>,
        limit: Option<i64>,
    ) -> AppResult<BlogPostsResponse> {
        let page = page.unwrap_or(1);
        let limit = limit.unwrap_or(10);

        let repo = self.database.blog_repository();
        let (posts, total_posts) = repo.get_all_posts(page, limit).await?;
        let total_pages = ((total_posts as f64) / (limit as f64)).ceil() as i64;

        Ok(BlogPostsResponse {
            posts,
            total_posts,
            total_pages,
        })
    }

    pub async fn get_dashboard_data(&self) -> AppResult<serde_json::Value> {
        let repo = self.database.blog_repository();

        // Get recent posts (limit 5)
        let (recent_posts, total_posts) = repo.get_all_posts(1, 5).await?;
        let categories = repo.get_all_categories().await?;
        let total_categories = categories.len() as i64;

        let latest_post = recent_posts.first().cloned();

        let dashboard_data = serde_json::json!({
            "success": true,
            "data": {
                "posts": recent_posts,
                "categories": categories,
                "recentPosts": recent_posts,
                "totalPosts": total_posts,
                "totalCategories": total_categories,
                "latestPost": latest_post
            }
        });

        Ok(dashboard_data)
    }

    pub async fn get_stats_trends(&self) -> AppResult<serde_json::Value> {
        let repo = self.database.blog_repository();

        // 计算过去7天和过去14天的统计数据来计算趋势
        let now = chrono::Utc::now();
        let seven_days_ago = now - chrono::Duration::days(7);
        let fourteen_days_ago = now - chrono::Duration::days(14);

        let seven_days_ago_str = seven_days_ago.to_rfc3339();
        let fourteen_days_ago_str = fourteen_days_ago.to_rfc3339();
        let now_str = now.to_rfc3339();

        // 获取过去7天的数据
        let recent_posts = repo
            .get_posts_count_by_date_range(&seven_days_ago_str, &now_str)
            .await?;
        let recent_categories = repo
            .get_categories_count_by_date_range(&seven_days_ago_str, &now_str)
            .await?;

        // 获取过去7-14天的数据用于比较
        let previous_posts = repo
            .get_posts_count_by_date_range(&fourteen_days_ago_str, &seven_days_ago_str)
            .await?;
        let previous_categories = repo
            .get_categories_count_by_date_range(&fourteen_days_ago_str, &seven_days_ago_str)
            .await?;

        // 计算增长百分比
        let posts_growth = if previous_posts > 0 {
            ((recent_posts as f64 - previous_posts as f64) / previous_posts as f64 * 100.0).round()
                as i64
        } else if recent_posts > 0 {
            100 // 如果之前没有文章，现在有了，就是100%增长
        } else {
            0
        };

        let categories_growth = recent_categories - previous_categories;

        // 模拟浏览量数据（实际项目中应该有真实的浏览量统计）
        let views_today = (recent_posts * 150 + 1000).max(1000); // 基于文章数量的简单估算
        let views_growth = if previous_posts > 0 {
            ((recent_posts as f64 / previous_posts as f64 - 1.0) * 100.0).round() as i64
        } else {
            20 // 默认增长
        }
        .max(5)
        .min(50); // 限制在5-50%之间

        let trends = serde_json::json!({
            "postsGrowth": posts_growth,
            "categoriesGrowth": categories_growth,
            "publishedGrowth": recent_posts,
            "viewsToday": views_today,
            "viewsGrowth": views_growth
        });

        Ok(trends)
    }
}
