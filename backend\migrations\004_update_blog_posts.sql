-- Update blog_posts table to support admin features
-- Check if we need to update the table structure

-- First, let's check if the new columns already exist
-- If they don't exist, we need to recreate the table

-- Create new blog_posts table with all required columns
CREATE TABLE IF NOT EXISTS blog_posts_new (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    excerpt TEXT NOT NULL,
    content TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    date TEXT NOT NULL, -- ISO 8601 format
    author TEXT DEFAULT 'Admin',
    read_time INTEGER DEFAULT 5,
    category TEXT DEFAULT 'General',
    tags TEXT DEFAULT '[]', -- JSON array as string
    featured BOOL<PERSON>N DEFAULT FALSE,
    status TEXT DEFAULT 'published',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Copy existing data if the old table exists and has data
INSERT OR IGNORE INTO blog_posts_new (id, title, excerpt, content, slug, date, author, read_time, category, tags, featured, status, created_at, updated_at)
SELECT
    id,
    title,
    excerpt,
    content,
    slug,
    date,
    'Admin' as author,
    5 as read_time,
    'General' as category,
    '[]' as tags,
    FALSE as featured,
    'published' as status,
    created_at,
    updated_at
FROM blog_posts;

-- Drop old table and rename new table
DROP TABLE IF EXISTS blog_posts;
ALTER TABLE blog_posts_new RENAME TO blog_posts;

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_blog_posts_slug ON blog_posts(slug);
CREATE INDEX IF NOT EXISTS idx_blog_posts_date ON blog_posts(date DESC);
CREATE INDEX IF NOT EXISTS idx_blog_posts_author ON blog_posts(author);
CREATE INDEX IF NOT EXISTS idx_blog_posts_category ON blog_posts(category);
CREATE INDEX IF NOT EXISTS idx_blog_posts_featured ON blog_posts(featured);
CREATE INDEX IF NOT EXISTS idx_blog_posts_status ON blog_posts(status);
